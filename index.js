const fs = require('fs');
const readline = require('readline');

const prefixes = [
    'Dear Anh/Chị:',
    'THÔNG BÁO LƯƠNG THÁNG',
    'Mã NV:',
    '<PERSON><PERSON><PERSON> vụ:',
    '<PERSON><PERSON> phận:',
    '<PERSON><PERSON><PERSON> công chuẩn',
    '1.<PERSON><PERSON><PERSON> công chính thức:',
    '2.<PERSON><PERSON><PERSON> công thử việc:',
    '3.<PERSON><PERSON><PERSON> công tháng  (3) = (1) + (2):',
    '4.<PERSON><PERSON><PERSON><PERSON> theo hợp đồng:',
    '4.<PERSON>hu nhập theo hợp đồng:',
    'D.<PERSON>h<PERSON> nhận  (D = B – C):'
];

const inputFile = 'demo.txt';
const outputFile = 'luong.v2.txt';

const rl = readline.createInterface({
    input: fs.createReadStream(inputFile, { encoding: 'utf8' }),
    crlfDelay: Infinity
});

const matchedLines = [];

rl.on('line', (line) => {
    if (prefixes.some(prefix => line.startsWith(prefix))) {
        matchedLines.push(line);
    }
});

rl.on('close', () => {
    fs.writeFileSync(outputFile, matchedLines.join('\n'), 'utf8');
    console.log(`Đã ghi ${matchedLines.length} dòng vào ${outputFile}`);
});
